<template>
	<ErrorBoundary
		:error-type="errorType"
		:error-message="error"
		:elderly-mode="isElderlyMode"
		@retry="retryLoad"
	>
		<view :class="[containerClass, 'fade-in', { 'elderly-mode': isElderlyMode }]">
			<!-- 自定义导航栏 - 优化版 -->
			<view class="custom-navbar slide-in-right">
				<view class="navbar-content">
					<view class="location-info ios-transition">
						<Icon name="location-line" size="md" color="white" class="location-icon ios-press-light" />
						<text class="location-text text-body">北京市朝阳区</text>
					</view>
					<view class="navbar-actions">
						<Icon
							name="notification-3-line"
							size="md"
							color="white"
							class="notification-icon ios-press"
							@click="showNotifications"
						/>
					</view>
				</view>
				<view class="status-bar"></view>
			</view>

		<!-- 顶部横幅区域 -->
		<view class="hero-section slide-up">
			<view class="hero-content">
				<view class="hero-text">
					<text class="hero-title scale-in">慧养老</text>
					<text class="hero-subtitle fade-in">为老年人群体</text>
					<text class="hero-subtitle fade-in">打造养老新生态</text>
				</view>
				<view class="hero-illustration bounce-in">
					<LazyImage
						src="/static/picture/zixun/W020211011780554733191.jpg"
						:width="300"
						:height="200"
						:border-radius="16"
						class="hero-image ios-transition"
						placeholder-icon="building-line"
						:show-placeholder="true"
					/>
				</view>
			</view>
		</view>

		<!-- 轮播图 -->
		<view class="banner-section scale-in" v-if="!loading && bannerList.length > 0">
			<swiper
				class="banner-swiper ios-transition"
				:indicator-dots="true"
				:autoplay="true"
				:interval="3000"
				:duration="500"
				indicator-color="rgba(255,255,255,0.5)"
				indicator-active-color="#ff8a00"
			>
				<swiper-item v-for="(banner, index) in bannerList" :key="index">
					<view class="banner-item ios-hover">
						<image :src="banner.image" class="banner-image ios-transition" mode="aspectFill"></image>
						<view class="banner-overlay">
							<view class="banner-content">
								<text class="banner-title">{{ banner.title }}</text>
								<text class="banner-subtitle">{{ banner.subtitle }}</text>
							</view>
						</view>
					</view>
				</swiper-item>
			</swiper>
		</view>

		<!-- 主要功能菜单 - 优化版 -->
		<view class="main-functions slide-up">
			<view class="function-row">
				<view class="function-item ios-press" @click="navigateTo('/pages/institution/list')">
					<view class="function-icon institution ios-transition">
						<Icon name="building-line" size="xl" color="white" institution />
					</view>
					<text class="function-text text-callout">选机构</text>
				</view>
				<view class="function-item ios-press" @click="navigateTo('/pages/service/list')">
					<view class="function-icon service ios-transition">
						<Icon name="search-line" size="xl" color="white" service />
					</view>
					<text class="function-text text-callout">找服务</text>
				</view>
				<view class="function-item ios-press" @click="navigateTo('/pages/subsidy/list')">
					<view class="function-icon subsidy ios-transition">
						<Icon name="money-cny-circle-line" size="xl" color="white" warning />
					</view>
					<text class="function-text text-callout">领补贴</text>
				</view>
				<view class="function-item ios-press" @click="navigateTo('/pages/elderly/settings')">
					<view class="function-icon elderly ios-transition">
						<Icon name="settings-line" size="xl" color="white" elderly />
					</view>
					<text class="function-text text-callout">适老版</text>
				</view>
			</view>
		</view>

		<!-- 紧急服务卡片 -->
		<view class="emergency-cards slide-in-right">
			<view class="emergency-card call-center ios-press" @click="callCenter">
				<view class="card-icon">
					<Icon name="customer-service-2-line" size="48rpx" color="white" class="ios-heartbeat" />
				</view>
				<view class="card-content">
					<text class="card-title">呼叫</text>
					<text class="card-title">中心</text>
				</view>
			</view>
			<view class="emergency-card guardian ios-press" @click="contactGuardian">
				<view class="card-icon">
					<Icon name="family-line" size="48rpx" color="white" />
				</view>
				<view class="card-content">
					<text class="card-title">联系</text>
					<text class="card-title">监护人</text>
				</view>
			</view>
			<view class="emergency-card service ios-press" @click="contactService">
				<view class="card-icon">
					<Icon name="user-heart-line" size="48rpx" color="white" />
				</view>
				<view class="card-content">
					<text class="card-title">联系</text>
					<text class="card-title">服务人</text>
				</view>
			</view>
		</view>
		
		<!-- 服务中心 - 优化版 -->
		<view class="service-center scale-in">
			<view class="section-title fade-in text-heading">服务中心</view>
			<view class="service-grid responsive-grid" :style="{ 'grid-template-columns': `repeat(${gridColumns}, 1fr)` }">
				<view class="service-item ios-press-light" @click="navigateTo('/pages/institution/list')">
					<view class="service-icon renovation ios-transition">
						<Icon name="building-line" size="lg" institution />
					</view>
					<text class="service-text text-caption">养老机构</text>
				</view>
				<view class="service-item ios-press-light" @click="navigateTo('/pages/service/list')">
					<view class="service-icon policy ios-transition">
						<Icon name="heart-3-line" size="lg" service />
					</view>
					<text class="service-text text-caption">养老服务</text>
				</view>
				<view class="service-item ios-press-light" @click="navigateTo('/pages/subsidy/list')">
					<view class="service-icon equipment ios-transition">
						<Icon name="money-cny-circle-line" size="32rpx" primary />
					</view>
					<text class="service-text">补贴申请</text>
				</view>
				<view class="service-item ios-press-light" @click="navigateTo('/pages/news/list')">
					<view class="service-icon bed ios-transition">
						<Icon name="article-line" size="32rpx" secondary />
					</view>
					<text class="service-text">资讯信息</text>
				</view>

				<view class="service-item ios-press-light" @click="navigateTo('/pages/health/index')">
					<view class="service-icon health-management ios-transition">
						<Icon name="health-book-line" size="32rpx" service />
					</view>
					<text class="service-text">健康管理</text>
				</view>
				<view class="service-item ios-press-light" @click="navigateTo('/pages/bed/index')">
					<view class="service-icon bed-service ios-transition">
						<Icon name="hotel-bed-line" size="32rpx" primary />
					</view>
					<text class="service-text">家庭床位</text>
				</view>
				<view class="service-item ios-press-light" @click="navigateTo('/pages/care-service/index')">
					<view class="service-icon care-service ios-transition">
						<Icon name="user-heart-line" size="32rpx" service />
					</view>
					<text class="service-text">护理服务</text>
				</view>
				<view class="service-item ios-press-light" @click="navigateTo('/pages/community-care/index')">
					<view class="service-icon community-care ios-transition">
						<Icon name="community-line" size="32rpx" service />
					</view>
					<text class="service-text">社区养老</text>
				</view>
				<view class="service-item ios-press-light" @click="navigateTo('/pages/elderly-dining/index')">
					<view class="service-icon elderly-dining ios-transition">
						<Icon name="restaurant-line" size="32rpx" elderly />
					</view>
					<text class="service-text">长者食堂</text>
				</view>
				<view class="service-item ios-press-light" @click="navigateTo('/pages/renovation/index')">
					<view class="service-icon renovation ios-transition">
						<Icon name="renovation-line" size="32rpx" primary />
					</view>
					<text class="service-text">适老改造</text>
				</view>
				<view class="service-item ios-press-light" @click="navigateTo('/pages/monitoring/index')">
					<view class="service-icon monitoring ios-transition">
						<Icon name="camera-line" size="32rpx" secondary />
					</view>
					<text class="service-text">智能监护</text>
				</view>
				<view class="service-item ios-press-light" @click="navigateTo('/pages/entertainment/index')">
					<view class="service-icon entertainment ios-transition">
						<Icon name="music-line" size="32rpx" elderly />
					</view>
					<text class="service-text">娱乐活动</text>
				</view>
			</view>
		</view>

		<!-- 资讯信息 - 优化版 -->
		<view class="news-section slide-up">
			<view class="section-header">
				<view class="section-title-container">
					<view class="section-title-with-icon fade-in">
						<Icon name="newspaper-line" size="lg" primary></Icon>
						<text class="section-title text-heading">最新资讯</text>
					</view>
					<text class="section-subtitle text-subheadline">了解最新养老政策与服务</text>
				</view>
				<text class="more-link ios-press-light text-callout" @click="navigateTo('/pages/news/list')">查看更多</text>
			</view>

			<!-- 资讯列表 -->
			<view class="news-list">
				<view
					class="news-item ios-press-light"
					v-for="(item, index) in newsList.slice(0, 6)"
					:key="item.id"
					@click="viewNews(item)"
				>
					<!-- 优化的图片容器 -->
					<view class="news-image-container">
						<!-- 图片加载状态 -->
						<view v-if="!item.image || item.imageError" class="news-icon-placeholder" :style="{ background: getCategoryBgColor(item.category) }">
							<Icon :name="getCategoryIcon(item.category)" size="48rpx" color="#fff"></Icon>
						</view>
						<!-- 实际图片 -->
						<LazyImage
							v-else
							:src="item.image"
							:width="160"
							:height="120"
							:border-radius="16"
							placeholder-icon="article-line"
							:show-placeholder="true"
							class="news-image ios-transition"
							@error="handleImageError(item)"
							@load="handleImageLoad(item)"
						/>
						<!-- 分类标签 -->
						<view class="category-tag">{{ item.category }}</view>
						<!-- 热门标签 -->
						<view v-if="item.isHot" class="hot-badge">
							<Icon name="fire-line" size="24rpx" color="#ff4444"></Icon>
							<text>热门</text>
						</view>
					</view>

					<view class="news-content">
						<text class="news-title">{{ item.title }}</text>
						<text class="news-summary">{{ item.summary }}</text>
						<view class="news-meta">
							<text class="news-author">{{ item.author }}</text>
							<text class="news-time">{{ formatTime(item.time) }}</text>
							<view class="news-stats">
								<view class="read-count">
									<Icon name="eye-line" size="20rpx" color="#999"></Icon>
									<text>{{ formatReadCount(item.readCount) }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 查看更多按钮 -->
			<view v-if="!loading && newsList.length > 6" class="load-more-container">
				<button class="load-more-btn ios-press-light" @click="navigateTo('/pages/news/list')">
					<text class="load-more-text">查看全部资讯</text>
					<text class="load-more-icon">→</text>
				</button>
			</view>
		</view>
	</view>
	</ErrorBoundary>
</template>

<script>
import Icon from '@/components/Icon/Icon.vue'
import ErrorBoundary from '@/components/ErrorBoundary/ErrorBoundary.vue'
import LazyImage from '@/components/LazyImage/LazyImage.vue'
import InteractionUtils from '@/utils/interactionUtils.js'

// 简化导入，避免复杂的响应式管理器
// import { responsiveManager } from '@/utils/responsiveUtils.js'

export default {
	components: {
		Icon,
		ErrorBoundary,
		LazyImage
	},
	data() {
		return {
			loading: false,
			error: null,
			errorType: 'unknown',
			bannerList: [],
			recommendList: [],
			newsList: [],
			statusBarHeight: 0,
			navBarHeight: 44,
			isNavigating: false, // 防止重复跳转
			currentBreakpoint: 'medium' // 当前响应式断点
		}
	},

	computed: {
		isElderlyMode() {
			return false // 暂时禁用适老版
		},

		// 响应式断点计算属性
		isSmallScreen() {
			return this.currentBreakpoint === 'small'
		},

		isTablet() {
			return this.currentBreakpoint === 'large' || this.currentBreakpoint === 'xlarge'
		},

		// 获取响应式网格列数
		gridColumns() {
			// 简化实现，根据屏幕宽度返回列数
			const systemInfo = uni.getSystemInfoSync()
			const width = systemInfo.screenWidth
			if (width < 375) return 2
			if (width < 768) return 3
			return 4
		},

		// 获取响应式容器类名
		containerClass() {
			return 'container'
		}
	},
	async onLoad() {
		console.log('首页加载开始')
		try {
			// 获取系统信息
			this.getSystemInfo()

			// 初始化响应式设计
			this.initResponsive()

			// 直接加载页面数据，不使用复杂的异步逻辑
			this.loadPageDataSync()

			console.log('首页加载完成')
		} catch (error) {
			console.error('首页加载失败:', error)
			this.handlePageError(error)
		}
	},

	onShow() {
		// 页面显示时重新检查响应式断点
		this.updateResponsive()
	},

	methods: {
		// 简化的同步数据加载方法
		loadPageDataSync() {
			console.log('开始同步加载页面数据')

			// 直接设置loading为false，立即显示内容
			this.loading = false
			this.error = null

			// 直接设置资讯数据
			this.newsList = [
				{
					id: 1,
					title: '养老服务新政策发布',
					summary: '政府出台新的养老服务补贴政策，惠及更多老年人，提升养老服务质量',
					image: '/picture/zixun/W020211011780554733191.jpg',
					time: '2024-01-15',
					category: '政策资讯',
					author: '民政部',
					readCount: 1256,
					isHot: true,
					imageError: false
				},
				{
					id: 2,
					title: '智慧养老技术创新突破',
					summary: '最新的AI智能设备和物联网技术助力老年人生活更便利、更安全',
					image: '/picture/zixun/71E00B4C613705188E11E072AC3B97F9A9493F32_size101_w1280_h853.jpg',
					time: '2024-01-14',
					category: '科技创新',
					author: '智慧养老研究院',
					readCount: 892,
					isHot: true,
					imageError: false
				},
				{
					id: 3,
					title: '社区养老服务全面升级',
					summary: '社区养老服务中心引入专业护理团队，提供24小时贴心服务',
					image: '/picture/zixun/8663976bbb664a0e9f6fd0ee564e5a8c.jpeg',
					time: '2024-01-13',
					category: '服务介绍',
					author: '社区服务中心',
					readCount: 743,
					isHot: false,
					imageError: false
				},
				{
					id: 4,
					title: '老年健康管理新模式',
					summary: '专业医护团队为老年人提供个性化健康管理服务，定期体检跟踪',
					image: '/picture/zixun/OIP-C.jpg',
					time: '2024-01-12',
					category: '健康知识',
					author: '健康管理中心',
					readCount: 567,
					isHot: false,
					imageError: false
				},
				{
					id: 5,
					title: '适老化改造惠民工程启动',
					summary: '为老年人家庭提供无障碍改造服务，包括扶手安装、防滑处理等',
					image: '/picture/zixun/R-C.jpg',
					time: '2024-01-11',
					category: '惠民工程',
					author: '住建部门',
					readCount: 423,
					isHot: false,
					imageError: false
				},
				{
					id: 6,
					title: '长者食堂营养配餐指南',
					summary: '营养师专业配制适合老年人的健康餐食，均衡营养促进健康',
					image: '/picture/zixun/0b0b-778029837c1616fbb2e33f0028be1b5d.jpg',
					time: '2024-01-10',
					category: '健康知识',
					author: '营养健康中心',
					readCount: 334,
					isHot: false,
					imageError: false
				}
			]

			// 设置其他数据
			this.bannerList = []
			this.recommendList = []

			console.log('页面数据同步加载完成，资讯数量:', this.newsList.length)
		},

		async loadPageData() {
			this.loading = true
			this.error = null

			try {
				console.log('开始加载页面数据')

				// 并行加载数据，增加超时处理
				const loadPromises = [
					this.loadBannerData(),
					this.loadRecommendData(),
					this.loadNewsData()
				]

				// 设置超时时间为10秒
				const timeoutPromise = new Promise((_, reject) => {
					setTimeout(() => reject(new Error('数据加载超时')), 10000)
				})

				const [bannerData, recommendData, newsData] = await Promise.race([
					Promise.all(loadPromises),
					timeoutPromise
				])

				this.bannerList = bannerData || []
				this.recommendList = recommendData || []
				this.newsList = newsData || []

				console.log('页面数据加载完成')
			} catch (error) {
				console.error('页面数据加载失败:', error)
				this.handlePageError(error)
			} finally {
				this.loading = false
			}
		},

		async loadBannerData() {
			// 模拟API调用
			return new Promise((resolve) => {
				setTimeout(() => {
					resolve([
						{
							image: '/static/picture/zixun/R-C.jpg',
							title: '温馨养老环境',
							subtitle: '花园式养老院，环境优美'
						},
						{
							image: '/static/picture/zixun/71E00B4C613705188E11E072AC3B97F9A9493F32_size101_w1280_h853.jpg',
							title: '专业护理服务',
							subtitle: '24小时贴心护理'
						},
						{
							image: '/static/picture/zixun/8663976bbb664a0e9f6fd0ee564e5a8c.jpeg',
							title: '丰富文娱活动',
							subtitle: '让老年生活更精彩'
						}
					])
				}, 500)
			})
		},

		async loadRecommendData() {
			return new Promise((resolve) => {
				setTimeout(() => {
					resolve([
						{
							id: 1,
							name: '阳光养老院',
							description: '专业的养老服务机构',
							image: '/static/picture/zixun/W020211011780554733191.jpg',
							rating: 4.8
						},
						{
							id: 2,
							name: '康乐老年公寓',
							description: '温馨舒适的居住环境',
							image: '/static/picture/zixun/OIP-C.jpg',
							rating: 4.6
						},
						{
							id: 3,
							name: '温馨护理中心',
							description: '贴心的护理服务',
							image: '/static/picture/zixun/R-C.jpg',
							rating: 4.9
						},
						{
							id: 4,
							name: '幸福老年之家',
							description: '家庭式温馨养老环境',
							image: '/static/picture/zixun/71E00B4C613705188E11E072AC3B97F9A9493F32_size101_w1280_h853.jpg',
							rating: 4.7
						},
						{
							id: 5,
							name: '爱心护理院',
							description: '专业医护团队服务',
							image: '/static/picture/zixun/8663976bbb664a0e9f6fd0ee564e5a8c.jpeg',
							rating: 4.5
						}
					])
				}, 300)
			})
		},

		async loadNewsData() {
			return new Promise((resolve) => {
				setTimeout(() => {
					resolve([
						{
							id: 1,
							title: '养老服务新政策发布',
							summary: '政府出台新的养老服务补贴政策，惠及更多老年人，提升养老服务质量',
							image: '/picture/zixun/W020211011780554733191.jpg',
							time: '2024-01-15',
							category: '政策资讯',
							author: '民政部',
							readCount: 1256,
							isHot: true,
							imageError: false
						},
						{
							id: 2,
							title: '智慧养老技术创新突破',
							summary: '最新的AI智能设备和物联网技术助力老年人生活更便利、更安全',
							image: '/picture/zixun/71E00B4C613705188E11E072AC3B97F9A9493F32_size101_w1280_h853.jpg',
							time: '2024-01-14',
							category: '科技创新',
							author: '智慧养老研究院',
							readCount: 892,
							isHot: true,
							imageError: false
						},
						{
							id: 3,
							title: '社区养老服务全面升级',
							summary: '社区养老服务中心引入专业护理团队，提供24小时贴心服务',
							image: '/picture/zixun/8663976bbb664a0e9f6fd0ee564e5a8c.jpeg',
							time: '2024-01-13',
							category: '服务介绍',
							author: '社区服务中心',
							readCount: 743,
							isHot: false,
							imageError: false
						},
						{
							id: 4,
							title: '老年健康管理新模式',
							summary: '专业医护团队为老年人提供个性化健康管理服务，定期体检跟踪',
							image: '/picture/zixun/OIP-C.jpg',
							time: '2024-01-12',
							category: '健康知识',
							author: '健康管理中心',
							readCount: 567,
							isHot: false,
							imageError: false
						},
						{
							id: 5,
							title: '适老化改造惠民工程启动',
							summary: '为老年人家庭提供无障碍改造服务，包括扶手安装、防滑处理等',
							image: '/picture/zixun/R-C.jpg',
							time: '2024-01-11',
							category: '惠民工程',
							author: '住建部门',
							readCount: 423,
							isHot: false,
							imageError: false
						},
						{
							id: 6,
							title: '长者食堂营养配餐指南',
							summary: '营养师专业配制适合老年人的健康餐食，均衡营养促进健康',
							image: '/picture/zixun/0b0b-778029837c1616fbb2e33f0028be1b5d.jpg',
							time: '2024-01-10',
							category: '健康知识',
							author: '营养健康中心',
							readCount: 334,
							isHot: false,
							imageError: false
						},
						{
							id: 7,
							title: '老年人数字生活技能培训',
							summary: '开展智能手机使用、网上购物等数字技能培训，帮助老年人融入数字时代',
							image: '/picture/zixun/R-C.jpg',
							time: '2024-01-09',
							category: '教育培训',
							author: '数字助老中心',
							readCount: 289,
							isHot: false,
							imageError: false
						},
						{
							id: 8,
							title: '养老机构服务质量评估',
							summary: '建立完善的养老机构服务质量评估体系，确保老年人享受优质服务',
							image: '/picture/zixun/OIP-C.jpg',
							time: '2024-01-08',
							category: '质量监管',
							author: '质量监督局',
							readCount: 198,
							isHot: false,
							imageError: false
						}
					])
				}, 400)
			})
		},

		handlePageError(error) {
			console.error('页面加载错误:', error)

			// 根据错误类型设置不同的错误信息
			let errorMessage = '页面加载失败'
			let errorType = 'unknown'

			if (error.message) {
				if (error.message.includes('超时')) {
					errorMessage = '网络连接超时，请检查网络后重试'
					errorType = 'network'
				} else if (error.message.includes('网络')) {
					errorMessage = '网络连接异常，请检查网络设置'
					errorType = 'network'
				} else if (error.message.includes('权限')) {
					errorMessage = '访问权限不足'
					errorType = 'permission'
				} else {
					errorMessage = error.message
					errorType = 'system'
				}
			}

			// 设置错误状态
			this.error = errorMessage
			this.errorType = errorType
		},

		retryLoad() {
			console.log('重试加载页面数据')
			this.error = null
			this.loadPageData()
		},

		getSystemInfo() {
			try {
				const systemInfo = uni.getSystemInfoSync()
				this.statusBarHeight = systemInfo.statusBarHeight || 44
				this.navBarHeight = systemInfo.platform === 'ios' ? 44 : 48
				console.log('系统信息获取成功:', {
					statusBarHeight: this.statusBarHeight,
					navBarHeight: this.navBarHeight,
					platform: systemInfo.platform
				})
			} catch (error) {
				console.error('获取系统信息失败:', error)
				// 使用默认值
				this.statusBarHeight = 44
				this.navBarHeight = 44
			}
		},

		// 初始化响应式设计
		initResponsive() {
			try {
				// 简化响应式逻辑
				const systemInfo = uni.getSystemInfoSync()
				const width = systemInfo.screenWidth

				if (width < 375) {
					this.currentBreakpoint = 'small'
				} else if (width < 768) {
					this.currentBreakpoint = 'medium'
				} else {
					this.currentBreakpoint = 'large'
				}

				console.log('当前响应式断点:', this.currentBreakpoint)
			} catch (error) {
				console.error('初始化响应式设计失败:', error)
				this.currentBreakpoint = 'medium'
			}
		},

		// 更新响应式断点
		updateResponsive() {
			// 简化实现，在小程序中屏幕尺寸通常不会动态变化
			this.initResponsive()
		},
		navigateTo(url) {
			return InteractionUtils.handleNavigation({
				url: url,
				type: 'navigateTo',
				loadingText: '跳转中...',
				showLoading: true,
				vibrate: true
			});
		},
		async showElderlyMode() {
			return InteractionUtils.handleButtonClick({
				callback: async () => {
					const confirmed = await new Promise(resolve => {
						uni.showModal({
							title: '适老版',
							content: '是否切换到适老版界面？\n适老版将提供更大字体和简化操作',
							success: (res) => resolve(res.confirm),
							fail: () => resolve(false)
						});
					});

					if (confirmed) {
						return await this.switchToElderlyMode();
					}
					return false;
				},
				showLoading: false,
				showSuccess: false
			});
		},
		async switchToElderlyMode() {
			return InteractionUtils.handleAsyncOperation({
				operation: async () => {
					// 模拟切换过程
					await new Promise(resolve => setTimeout(resolve, 1500));

					// 这里可以添加实际的适老版切换逻辑
					// 比如修改全局样式、字体大小等
					uni.setStorageSync('elderlyMode', true);

					return true;
				},
				loadingText: '正在切换...',
				successText: '已切换到适老版',
				errorText: '切换失败，请重试',
				operationId: 'switch_elderly_mode'
			});
		},
		callCenter() {
			return InteractionUtils.handlePhoneCall({
				phoneNumber: '************',
				confirmText: '即将拨打客服热线：************\n确定要拨打吗？'
			});
		},
		contactGuardian() {
			return InteractionUtils.handleEmergencyAction({
				actionType: 'contact_guardian',
				confirmText: '确定要联系监护人吗？',
				callback: async () => {
					// 模拟联系过程
					await new Promise(resolve => setTimeout(resolve, 1000));
					return true;
				}
			});
		},
		contactService() {
			return InteractionUtils.handleEmergencyAction({
				actionType: 'contact_service',
				confirmText: '确定要联系服务人员吗？',
				callback: async () => {
					// 模拟联系过程
					await new Promise(resolve => setTimeout(resolve, 1000));
					return true;
				}
			});
		},
		viewInstitution(item) {
			return InteractionUtils.handleNavigation({
				url: `/pages/institution/detail`,
				params: { id: item.id },
				loadingText: '加载机构详情...'
			});
		},
		viewNews(item) {
			return InteractionUtils.handleNavigation({
				url: `/pages/news/detail`,
				params: { id: item.id },
				loadingText: '加载资讯详情...'
			});
		},

		// 处理图片加载错误
		handleImageError(item) {
			console.log('首页资讯图片加载失败:', item.image);

			// 如果还没有尝试过备用图片，则尝试使用备用图片
			if (!item.hasTriedBackup) {
				item.hasTriedBackup = true;

				// 根据分类选择合适的备用图片
				const categoryBackupMap = {
					'政策资讯': '/picture/zixun/W020211011780554733191.jpg',
					'科技创新': '/picture/zixun/71E00B4C613705188E11E072AC3B97F9A9493F32_size101_w1280_h853.jpg',
					'服务介绍': '/picture/zixun/8663976bbb664a0e9f6fd0ee564e5a8c.jpeg',
					'健康知识': '/picture/zixun/OIP-C.jpg',
					'惠民工程': '/picture/zixun/R-C.jpg',
					'教育培训': '/picture/zixun/R-C.jpg',
					'质量监管': '/picture/zixun/OIP-C.jpg'
				};

				const backupImage = categoryBackupMap[item.category] || '/picture/zixun/W020211011780554733191.jpg';

				// 如果当前图片不是备用图片，则尝试使用备用图片
				if (item.image !== backupImage) {
					console.log(`尝试使用${item.category}分类的备用图片:`, backupImage);
					item.image = backupImage;
					item.imageError = false;
					// 强制更新视图
					this.$forceUpdate();
					return;
				}
			}

			// 如果备用图片也失败了，则显示图标占位符
			console.log('所有图片都加载失败，显示分类图标占位符');
			item.imageError = true;
		},

		// 处理图片加载成功
		handleImageLoad(item) {
			console.log('首页资讯图片加载成功:', item.image);
			item.imageError = false;
		},

		// 获取分类对应的图标
		getCategoryIcon(category) {
			const iconMap = {
				'政策资讯': 'government-line',
				'科技创新': 'rocket-line',
				'服务介绍': 'service-line',
				'健康知识': 'heart-pulse-line',
				'惠民工程': 'building-line',
				'教育培训': 'graduation-cap-line',
				'质量监管': 'shield-check-line'
			};
			return iconMap[category] || 'article-line';
		},

		// 获取分类对应的背景色
		getCategoryBgColor(category) {
			const colorMap = {
				'政策资讯': 'linear-gradient(135deg, #ff8a00 0%, #ff6b35 100%)',
				'科技创新': 'linear-gradient(135deg, #2196f3 0%, #1976d2 100%)',
				'服务介绍': 'linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)',
				'健康知识': 'linear-gradient(135deg, #4caf50 0%, #388e3c 100%)',
				'惠民工程': 'linear-gradient(135deg, #607d8b 0%, #455a64 100%)',
				'教育培训': 'linear-gradient(135deg, #ff9800 0%, #f57c00 100%)',
				'质量监管': 'linear-gradient(135deg, #795548 0%, #5d4037 100%)'
			};
			return colorMap[category] || 'linear-gradient(135deg, #666 0%, #555 100%)';
		},

		// 格式化阅读数量
		formatReadCount(count) {
			if (count >= 10000) {
				return (count / 10000).toFixed(1) + 'w'
			} else if (count >= 1000) {
				return (count / 1000).toFixed(1) + 'k'
			}
			return count.toString()
		},

		// 格式化时间显示
		formatTime(timeStr) {
			const now = new Date()
			const time = new Date(timeStr)
			const diff = now - time
			const days = Math.floor(diff / (1000 * 60 * 60 * 24))

			if (days === 0) {
				return '今天'
			} else if (days === 1) {
				return '昨天'
			} else if (days <= 7) {
				return `${days}天前`
			} else {
				return timeStr
			}
		},

		// 显示通知
		showNotifications() {
			return InteractionUtils.handleButtonClick({
				callback: () => {
					return Promise.resolve(false);
				},
				loadingText: '加载通知...',
				successText: '',
				errorText: '通知功能开发中',
				showLoading: false,
				showSuccess: false
			});
		},

		// 切换适老版模式
		toggleElderlyMode() {
			return InteractionUtils.handleButtonClick({
				callback: async () => {
					const currentMode = uni.getStorageSync('elderlyMode') || false;
					const newMode = !currentMode;
					uni.setStorageSync('elderlyMode', newMode);
					return newMode;
				},
				loadingText: '切换中...',
				successText: this.isElderlyMode ? '已关闭适老版' : '已开启适老版',
				errorText: '切换失败',
				operationId: 'toggle_elderly_mode'
			});
		},

		// 显示即将推出提示
		showComingSoon() {
			return InteractionUtils.handleButtonClick({
				callback: () => {
					return Promise.resolve(false);
				},
				loadingText: '',
				successText: '',
				errorText: '功能即将推出',
				showLoading: false,
				showSuccess: false
			});
		}
	}
}
</script>

<style scoped>
/* ================================
   iOS风格首页样式
   基于iOS Human Interface Guidelines
   ================================ */

.container {
	background: linear-gradient(135deg, var(--primary-color, #ff8a00) 0%, var(--primary-dark, #e67700) 100%);
	min-height: 100vh;
	padding-bottom: var(--spacing-32, 64rpx); /* 底部安全间距 */
}

/* iOS风格自定义导航栏 - 使用设计系统 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1000;
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(30rpx) saturate(1.8);
	-webkit-backdrop-filter: blur(30rpx) saturate(1.8);
	border-bottom: 1rpx solid rgba(255, 255, 255, 0.25);
	box-shadow: var(--shadow-md, 0 4rpx 16rpx rgba(0, 0, 0, 0.06));
}

.status-bar {
	height: var(--status-bar-height, 44rpx);
	background: transparent; /* 确保状态栏背景透明 */
}

.navbar-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 20rpx 32rpx; /* 增加垂直间距，更符合iOS */
	color: white;
	min-height: 88rpx; /* iOS标准导航栏高度 */
	position: relative;
}

.location-info {
	display: flex;
	align-items: center;
}

.location-icon {
	font-size: 32rpx;
	margin-right: 8rpx; /* iOS标准间距 */
}

.location-text {
	font-size: 34rpx; /* iOS Body字体 */
	font-weight: 500; /* iOS Medium字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'SF Pro Text', sans-serif;
}

.navbar-actions {
	display: flex;
	align-items: center;
}

.notification-icon {
	font-size: 36rpx;
	padding: 8rpx; /* 增加触摸区域 */
	border-radius: 50%;
	transition: background-color 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.notification-icon:active {
	background-color: rgba(255, 255, 255, 0.2);
}

/* iOS风格顶部横幅区域 */
.hero-section {
	padding: 140rpx 32rpx 32rpx; /* iOS标准间距 */
	color: white;
}

.hero-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.hero-text {
	flex: 1;
}

.hero-title {
	font-size: 68rpx; /* iOS Large Title字体 */
	font-weight: 700; /* iOS Bold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
	display: block;
	margin-bottom: 16rpx; /* iOS标准间距 */
	letter-spacing: -0.02em; /* iOS字母间距 */
}

.hero-subtitle {
	font-size: 36rpx; /* iOS Headline字体 */
	font-weight: 400; /* iOS Regular字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	display: block;
	line-height: 1.47; /* iOS标准行高 */
	opacity: 0.85; /* iOS次要文字透明度 */
}

.hero-illustration {
	width: 200rpx;
	height: 200rpx;
	border-radius: 24rpx; /* iOS大圆角 */
	overflow: hidden;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2); /* iOS阴影 */
}

.hero-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

/* iOS风格轮播图样式 */
.banner-section {
	margin: 32rpx 24rpx; /* iOS标准间距 */
	border-radius: 24rpx; /* iOS大圆角 */
	overflow: hidden;
	box-shadow:
		0 4rpx 16rpx rgba(0, 0, 0, 0.08),
		0 8rpx 32rpx rgba(0, 0, 0, 0.12); /* iOS分层阴影 */
}

.banner-swiper {
	height: 300rpx;
	border-radius: 24rpx;
}

/* iOS风格响应式适配 */
@media screen and (max-width: 375px) {
	.banner-swiper {
		height: 240rpx;
	}
}

@media screen and (min-width: 768px) {
	.banner-swiper {
		height: 360rpx;
	}
}

.banner-item {
	position: relative;
	width: 100%;
	height: 100%;
}

.banner-image {
	width: 100%;
	height: 100%;
	border-radius: 24rpx; /* 与容器圆角保持一致 */
	object-fit: cover;
}

.banner-overlay {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: linear-gradient(transparent, rgba(0, 0, 0, 0.5)); /* 更柔和的渐变 */
	padding: 32rpx 24rpx 24rpx; /* iOS标准间距 */
	border-radius: 0 0 24rpx 24rpx;
}

.banner-content {
	color: white;
}

.banner-title {
	font-size: 36rpx; /* iOS Headline字体 */
	font-weight: 600; /* iOS Semibold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
	display: block;
	margin-bottom: 8rpx; /* iOS标准间距 */
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3); /* iOS阴影 */
	letter-spacing: -0.01em;
}

.banner-subtitle {
	font-size: 30rpx; /* iOS Subheadline字体 */
	font-weight: 400; /* iOS Regular字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	opacity: 0.85;
	display: block;
	text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

/* iOS风格主要功能菜单 - 使用设计系统 */
.main-functions {
	padding: 0 var(--spacing-12, 24rpx);
	margin-bottom: var(--spacing-20, 40rpx);
}

.function-row {
	display: flex;
	justify-content: space-between;
	gap: var(--spacing-6, 12rpx);
	background: rgba(255, 255, 255, 0.1);
	border-radius: var(--radius-2xl, 24rpx);
	padding: var(--spacing-12, 24rpx) var(--spacing-8, 16rpx);
	backdrop-filter: blur(10rpx);
	-webkit-backdrop-filter: blur(10rpx);
	box-shadow: var(--shadow-card, 0 2rpx 4rpx rgba(0, 0, 0, 0.02), 0 4rpx 8rpx rgba(0, 0, 0, 0.04));
}

.function-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx 12rpx; /* 增加内边距 */
	border-radius: 20rpx; /* 添加项目圆角 */
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94); /* 延长过渡时间 */
	position: relative;
	overflow: hidden;
}

.function-item:active {
	transform: scale(0.92); /* 更明显的按压效果 */
	background: rgba(255, 255, 255, 0.15); /* 按压时的背景变化 */
}

.function-icon {
	width: 100rpx; /* 稍微减小图标尺寸 */
	height: 100rpx;
	border-radius: 24rpx; /* iOS更大的圆角 */
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 16rpx; /* 增加与文字的间距 */
	box-shadow:
		0 2rpx 8rpx rgba(0, 0, 0, 0.1),
		0 4rpx 16rpx rgba(0, 0, 0, 0.08); /* iOS分层阴影 */
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
}

.function-icon:active {
	transform: scale(0.95);
	box-shadow:
		0 1rpx 4rpx rgba(0, 0, 0, 0.12),
		0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.function-icon.institution {
	background-color: #ff6b6b; /* 保持现有颜色 */
}

.function-icon.service {
	background-color: #4ecdc4; /* 保持现有颜色 */
}

.function-icon.subsidy {
	background-color: #45b7d1; /* 保持现有颜色 */
}

.function-icon.elderly {
	background-color: #96ceb4; /* 保持现有颜色 */
}

.icon-text {
	font-size: 48rpx;
	color: white;
}

.function-text {
	font-size: 30rpx; /* iOS Subheadline字体 */
	color: white;
	font-weight: 500; /* iOS Medium字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	text-align: center;
	line-height: 1.2;
}

/* iOS风格紧急服务卡片 - 增强设计 */
.emergency-cards {
	padding: 0 24rpx; /* 与功能菜单保持一致的间距 */
	margin-bottom: 40rpx; /* 增加底部间距 */
	display: flex;
	gap: 12rpx; /* 减少间距，更紧凑 */
}

.emergency-card {
	flex: 1;
	height: 140rpx; /* 稍微减小高度，更精致 */
	border-radius: 24rpx; /* 增大圆角，更符合iOS */
	padding: 20rpx; /* 调整内边距 */
	display: flex;
	align-items: center;
	color: white;
	position: relative;
	overflow: hidden;
	box-shadow:
		0 2rpx 8rpx rgba(0, 0, 0, 0.1),
		0 4rpx 20rpx rgba(0, 0, 0, 0.15),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.2); /* 添加内阴影高光 */
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 添加iOS风格的光泽效果 */
.emergency-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 50%;
	background: linear-gradient(180deg, rgba(255, 255, 255, 0.15) 0%, transparent 100%);
	border-radius: 24rpx 24rpx 0 0;
	pointer-events: none;
}

.emergency-card:active {
	transform: scale(0.94); /* 更明显的按压效果 */
	box-shadow:
		0 1rpx 4rpx rgba(0, 0, 0, 0.15),
		0 2rpx 12rpx rgba(0, 0, 0, 0.2),
		inset 0 1rpx 0 rgba(255, 255, 255, 0.1);
}

.emergency-card.call-center {
	background: linear-gradient(135deg, #FF3B30 0%, #E53E3E 100%); /* 添加微妙渐变 */
}

.emergency-card.guardian {
	background: linear-gradient(135deg, #FF9500 0%, #ED8936 100%); /* 添加微妙渐变 */
}

.emergency-card.service {
	background: linear-gradient(135deg, #34C759 0%, #38A169 100%); /* 添加微妙渐变 */
}

.card-icon {
	margin-right: 16rpx; /* iOS标准间距 */
}

.card-icon-text {
	font-size: 48rpx;
	color: white;
}

.card-content {
	flex: 1;
}

.card-title {
	font-size: 34rpx; /* iOS Body字体 */
	font-weight: 600; /* iOS Semibold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
	display: block;
	line-height: 1.2;
}

/* iOS风格服务中心 - 使用设计系统 */
.service-center {
	background: var(--background-primary, rgba(255, 255, 255, 0.95));
	backdrop-filter: blur(20rpx);
	-webkit-backdrop-filter: blur(20rpx);
	margin: 0 var(--spacing-12, 24rpx) var(--spacing-20, 40rpx);
	border-radius: var(--radius-3xl, 28rpx);
	padding: var(--spacing-18, 36rpx) var(--spacing-16, 32rpx);
	box-shadow: var(--shadow-modal, 0 8rpx 16rpx rgba(0, 0, 0, 0.08), 0 16rpx 32rpx rgba(0, 0, 0, 0.12));
	border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.section-title {
	font-size: 44rpx; /* 使用iOS Title 2字体，更突出 */
	font-weight: 700; /* 使用Bold字重，更醒目 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
	color: #1F2937; /* iOS标准文字色 */
	margin-bottom: 28rpx; /* 增加底部间距 */
	letter-spacing: -0.02em; /* 更紧密的字母间距 */
	text-align: left; /* 确保左对齐 */
}

.service-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 20rpx; /* 稍微减少间距，让布局更紧凑 */
	align-items: start; /* 顶部对齐 */
}

.service-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 16rpx 8rpx; /* iOS标准间距 */
	border-radius: 16rpx; /* iOS圆角 */
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.service-item:active {
	transform: scale(0.95); /* iOS按压效果 */
	background-color: rgba(0, 0, 0, 0.04);
}

.service-icon {
	width: 100rpx;
	height: 100rpx;
	border-radius: 20rpx; /* iOS大圆角 */
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 12rpx; /* iOS标准间距 */
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04); /* iOS轻微阴影 */
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.service-icon:active {
	transform: scale(0.95);
	box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.08);
}

/* iOS风格服务图标颜色 - 使用更柔和的色彩 */
.service-icon.renovation {
	background-color: rgba(52, 199, 89, 0.1); /* iOS绿色背景 */
}

.service-icon.policy {
	background-color: rgba(255, 149, 0, 0.1); /* iOS橙色背景 */
}

.service-icon.equipment {
	background-color: rgba(0, 122, 255, 0.1); /* iOS蓝色背景 */
}

.service-icon.bed {
	background-color: rgba(255, 45, 85, 0.1); /* iOS粉色背景 */
}

.service-icon.elderly-station {
	background-color: rgba(175, 82, 222, 0.1); /* iOS紫色背景 */
}

.service-icon.community-care {
	background-color: rgba(52, 199, 89, 0.1); /* iOS绿色背景 */
}

.service-icon.elderly-dining {
	background-color: rgba(255, 204, 0, 0.1); /* iOS黄色背景 */
}

.service-icon.care-service {
	background-color: rgba(255, 59, 48, 0.1); /* iOS红色背景 */
}

.service-icon.health {
	background-color: rgba(52, 199, 89, 0.1); /* iOS绿色背景 */
}

.service-icon.monitoring {
	background-color: rgba(0, 122, 255, 0.1); /* iOS蓝色背景 */
}

.service-icon.entertainment {
	background-color: rgba(255, 149, 0, 0.1); /* iOS橙色背景 */
}

.service-icon.data-manage {
	background-color: rgba(255, 138, 0, 0.1); /* 保持品牌色 */
}

.service-icon.icon-gallery {
	background-color: rgba(0, 122, 255, 0.1); /* iOS蓝色背景 */
}

.service-icon.icon-showcase {
	background-color: rgba(255, 138, 0, 0.1); /* 品牌色背景 */
}

.service-icon.icon-validator {
	background-color: rgba(52, 199, 89, 0.1); /* iOS绿色背景 */
}

.service-icon.navigation-tester {
	background-color: rgba(255, 138, 0, 0.1); /* 品牌色背景 */
}

.service-icon.interaction-tester {
	background-color: rgba(52, 199, 89, 0.1); /* iOS绿色背景 */
}

.service-icon.image-validator {
	background-color: rgba(255, 138, 0, 0.1); /* 品牌色背景 */
}

.service-icon-text {
	font-size: 36rpx;
	color: #6B7280; /* iOS中性文字色 */
}

.service-text {
	font-size: 26rpx; /* iOS Footnote字体 */
	color: #6B7280; /* iOS中性文字色 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	text-align: center;
	line-height: 1.2;
	font-weight: 400;
}

/* iOS风格资讯信息 - 全面优化 */
.news-section {
	background: rgba(255, 255, 255, 0.95); /* 半透明白色背景 */
	backdrop-filter: blur(20rpx); /* 毛玻璃效果 */
	-webkit-backdrop-filter: blur(20rpx);
	margin: 0 24rpx 40rpx; /* 与其他组件保持一致的间距 */
	border-radius: 28rpx; /* 更大的圆角，更现代 */
	padding: 36rpx 32rpx; /* 增加顶部内边距 */
	box-shadow:
		0 2rpx 8rpx rgba(0, 0, 0, 0.04),
		0 4rpx 24rpx rgba(0, 0, 0, 0.06),
		0 8rpx 40rpx rgba(0, 0, 0, 0.04); /* 增强分层阴影 */
	border: 1rpx solid rgba(255, 255, 255, 0.2); /* 添加边框 */
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 32rpx; /* 增加底部间距 */
}

.section-title-container {
	flex: 1;
}

.section-title-with-icon {
	display: flex;
	align-items: center;
	gap: 12rpx;
	margin-bottom: 8rpx;
}

.section-title {
	font-size: 44rpx; /* iOS Title 2字体 */
	font-weight: 700; /* iOS Bold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
	color: #1F2937; /* iOS标准文字色 */
	margin-bottom: 8rpx;
	letter-spacing: -0.01em;
	display: block;
}

.section-subtitle {
	font-size: 28rpx; /* iOS Subheadline字体 */
	color: #6B7280; /* iOS次要文字色 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	font-weight: 400;
	display: block;
}

.more-link {
	font-size: 32rpx; /* 使用iOS Callout字体 */
	color: #ff8a00; /* 保持品牌色 */
	font-weight: 600; /* 使用Semibold字重，更醒目 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	padding: 12rpx 20rpx; /* 增加触摸区域 */
	border-radius: 20rpx; /* 添加圆角 */
	background: rgba(255, 138, 0, 0.1); /* 添加背景色 */
	border: 1rpx solid rgba(255, 138, 0, 0.2);
}

.more-link:active {
	opacity: 0.7;
	background: rgba(255, 138, 0, 0.15); /* 按压时背景变化 */
	transform: scale(0.95); /* 添加按压缩放效果 */
}

.news-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx; /* 增加间距 */
}

.news-item {
	display: flex;
	background-color: #F9FAFB; /* iOS浅背景色 */
	border-radius: 20rpx; /* 更大的圆角 */
	overflow: visible; /* 允许热门标签溢出 */
	padding: 20rpx; /* 增加内边距 */
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
	border: 1rpx solid rgba(0, 0, 0, 0.05);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.news-item:active {
	transform: scale(0.98); /* iOS按压效果 */
	background-color: #F3F4F6;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}

/* 热门标签 */
.hot-badge {
	position: absolute;
	top: -6rpx;
	right: -6rpx;
	background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
	color: white;
	font-size: 18rpx;
	font-weight: 700;
	padding: 6rpx 10rpx;
	border-radius: 12rpx;
	z-index: 15;
	box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.4);
	border: 2rpx solid white;
	animation: hot-badge-pulse 2s infinite;
	transform-origin: center;
	display: flex;
	align-items: center;
	gap: 4rpx;
}

@keyframes hot-badge-pulse {
	0%, 100% { transform: scale(1); }
	50% { transform: scale(1.05); }
}

.news-image-container {
	flex-shrink: 0;
	width: 160rpx;
	height: 120rpx;
	margin-right: 20rpx; /* 增加间距 */
	overflow: hidden;
	border-radius: 16rpx; /* 更大的圆角 */
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08); /* 增强阴影 */
	position: relative;
	background: #f8f9fa; /* 添加背景色 */
}

/* 图片加载动画 */
.news-image-container::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
	transform: translateX(-100%);
	animation: image-loading-shimmer 1.5s infinite;
	z-index: 1;
	pointer-events: none;
	opacity: 0;
	transition: opacity 0.3s ease;
}

.news-image-container.loading::before {
	opacity: 1;
}

@keyframes image-loading-shimmer {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

.news-image {
	width: 100%;
	height: 100%;
	border-radius: 16rpx;
	object-fit: cover;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	opacity: 0;
	animation: image-fade-in 0.5s ease forwards;
}

@keyframes image-fade-in {
	from { opacity: 0; transform: scale(1.1); }
	to { opacity: 1; transform: scale(1); }
}

.news-image:active {
	transform: scale(1.02); /* 更明显的缩放 */
}

/* 分类标签 */
.category-tag {
	position: absolute;
	bottom: 8rpx;
	left: 8rpx;
	background: rgba(0, 0, 0, 0.7);
	color: white;
	font-size: 18rpx;
	font-weight: 500;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
	backdrop-filter: blur(10rpx);
	-webkit-backdrop-filter: blur(10rpx);
}

.news-icon-placeholder {
	width: 100%;
	height: 100%;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	overflow: hidden;
	animation: placeholder-fade-in 0.3s ease forwards;
}

@keyframes placeholder-fade-in {
	from { opacity: 0; transform: scale(0.9); }
	to { opacity: 1; transform: scale(1); }
}

/* 图标占位符闪烁动画 */
.news-icon-placeholder::after {
	content: '';
	position: absolute;
	top: -50%;
	left: -50%;
	width: 200%;
	height: 200%;
	background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
	transform: rotate(45deg);
	animation: icon-shimmer 2s infinite;
}

@keyframes icon-shimmer {
	0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
	100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.news-content {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	min-height: 120rpx;
}

.news-title {
	font-size: 32rpx; /* iOS Callout字体 */
	font-weight: 600; /* iOS Semibold字重 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', sans-serif;
	color: #1F2937; /* iOS标准文字色 */
	margin-bottom: 12rpx; /* 增加间距 */
	line-height: 1.4;
	letter-spacing: -0.01em;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2; /* 限制两行 */
	overflow: hidden;
}

.news-summary {
	font-size: 26rpx; /* iOS Footnote字体 */
	color: #6B7280; /* iOS次要文字色 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	margin-bottom: 16rpx; /* 增加间距 */
	line-height: 1.5; /* 增加行高 */
	font-weight: 400;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2; /* 限制两行 */
	overflow: hidden;
}

/* 资讯元信息 */
.news-meta {
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: wrap;
	gap: 8rpx;
}

.news-author {
	font-size: 22rpx; /* iOS Caption字体 */
	color: #ff8a00; /* 品牌色 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	font-weight: 500;
	background: rgba(255, 138, 0, 0.1);
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.news-time {
	font-size: 22rpx; /* iOS Caption字体 */
	color: #9CA3AF; /* iOS三级文字色 */
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	font-weight: 400;
}

.news-stats {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.read-count {
	font-size: 20rpx;
	color: #9CA3AF;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	font-weight: 400;
	display: flex;
	align-items: center;
	gap: 6rpx;
}

/* 查看更多按钮 */
.load-more-container {
	margin-top: 24rpx;
	text-align: center;
}

.load-more-btn {
	background: linear-gradient(135deg, #ff8a00 0%, #f57c00 100%);
	color: white;
	border: none;
	border-radius: 24rpx;
	padding: 16rpx 32rpx;
	font-size: 28rpx;
	font-weight: 600;
	font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', sans-serif;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	box-shadow: 0 4rpx 16rpx rgba(255, 138, 0, 0.3);
	min-width: 200rpx;
	margin: 0 auto;
}

.load-more-btn:active {
	transform: scale(0.95);
	box-shadow: 0 2rpx 8rpx rgba(255, 138, 0, 0.4);
}

.load-more-text {
	font-size: 28rpx;
	font-weight: 600;
}

.load-more-icon {
	font-size: 24rpx;
	font-weight: bold;
	transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.load-more-btn:active .load-more-icon {
	transform: translateX(4rpx);
}

/* ================================
   iOS风格响应式优化
   ================================ */

/* iPhone SE 及小屏设备优化 */
@media screen and (max-width: 375px) {
	.function-row {
		padding: 20rpx 12rpx; /* 减少内边距 */
	}

	.function-icon {
		width: 80rpx; /* 减小图标尺寸 */
		height: 80rpx;
		border-radius: 20rpx;
	}

	.service-grid {
		grid-template-columns: repeat(3, 1fr); /* 改为3列布局 */
		gap: 16rpx;
	}

	.emergency-card {
		height: 120rpx; /* 减小高度 */
		padding: 16rpx;
	}
}

/* iPad 及大屏设备优化 */
@media screen and (min-width: 768px) {
	.container {
		max-width: 1024px;
		margin: 0 auto;
	}

	.function-row {
		max-width: 600rpx;
		margin: 0 auto;
	}

	.service-grid {
		grid-template-columns: repeat(6, 1fr); /* 增加到6列 */
		max-width: 800rpx;
		margin: 0 auto;
	}

	.emergency-cards {
		max-width: 600rpx;
		margin: 0 auto 40rpx;
	}
}

/* ================================
   iOS风格适老化增强 - 升级版
   ================================ */

/* 适老化标题系统 - 基于iOS Typography */
.elderly-mode .hero-title,
.ios-elderly-mode .hero-title {
	font-size: calc(68rpx * 1.3) !important; /* Large Title * 1.3 */
	font-weight: 700 !important; /* iOS Bold */
	letter-spacing: -0.02em !important; /* iOS标准字母间距 */
	line-height: 1.4 !important; /* 优化行高 */
}

.elderly-mode .hero-subtitle,
.ios-elderly-mode .hero-subtitle {
	font-size: calc(36rpx * 1.3) !important; /* Headline * 1.3 */
	font-weight: 600 !important; /* iOS Semibold */
	line-height: 1.5 !important;
}

.elderly-mode .section-title,
.ios-elderly-mode .section-title {
	font-size: calc(44rpx * 1.3) !important; /* Title 2 * 1.3 */
	font-weight: 700 !important; /* iOS Bold */
	letter-spacing: -0.01em !important;
	line-height: 1.3 !important;
}

/* 适老化功能文字 - iOS风格 */
.elderly-mode .function-text,
.ios-elderly-mode .function-text {
	font-size: calc(30rpx * 1.3) !important; /* Subheadline * 1.3 */
	font-weight: 600 !important; /* iOS Semibold */
	line-height: 1.4 !important;
	text-align: center !important;
}

.elderly-mode .service-text,
.ios-elderly-mode .service-text {
	font-size: calc(26rpx * 1.3) !important; /* Footnote * 1.3 */
	font-weight: 600 !important; /* 增强字重 */
	line-height: 1.5 !important;
	text-align: center !important;
}

/* 适老化卡片文字 - iOS风格 */
.elderly-mode .card-title,
.ios-elderly-mode .card-title {
	font-size: calc(34rpx * 1.3) !important; /* Body * 1.3 */
	font-weight: 700 !important; /* iOS Bold */
	line-height: 1.3 !important;
}

.elderly-mode .news-title,
.ios-elderly-mode .news-title {
	font-size: calc(32rpx * 1.3) !important; /* Callout * 1.3 */
	font-weight: 700 !important; /* iOS Bold */
	line-height: 1.4 !important;
}

.elderly-mode .news-summary,
.ios-elderly-mode .news-summary {
	font-size: calc(26rpx * 1.3) !important; /* Footnote * 1.3 */
	font-weight: 500 !important; /* iOS Medium */
	line-height: 1.6 !important; /* 增加行高提升可读性 */
}

.elderly-mode .news-time,
.ios-elderly-mode .news-time {
	font-size: calc(22rpx * 1.3) !important; /* Caption * 1.3 */
	font-weight: 500 !important;
	line-height: 1.4 !important;
}

/* 适老化触摸目标增强 - iOS风格 */
.elderly-mode .function-item,
.ios-elderly-mode .function-item {
	padding: 28rpx 20rpx !important; /* 增大内边距 */
	min-height: 112rpx !important; /* 56pt触摸目标 */
	border-radius: 24rpx !important; /* iOS大圆角 */
	border: 2rpx solid #8e8e93 !important; /* iOS标准边框 */
}

.elderly-mode .function-icon,
.ios-elderly-mode .function-icon {
	width: 120rpx !important; /* 增大图标容器 */
	height: 120rpx !important;
	border-radius: 30rpx !important; /* 更大圆角 */
	margin-bottom: 20rpx !important; /* 增加间距 */
}

.elderly-mode .emergency-card,
.ios-elderly-mode .emergency-card {
	height: 180rpx !important; /* 增加高度到90pt */
	padding: 28rpx !important; /* 增大内边距 */
	border-radius: 28rpx !important; /* 更大圆角 */
	border: 2rpx solid rgba(255, 255, 255, 0.3) !important; /* 半透明边框 */
}

.elderly-mode .service-item,
.ios-elderly-mode .service-item {
	padding: 24rpx 16rpx !important; /* 增大内边距 */
	min-height: 112rpx !important; /* 56pt触摸目标 */
	border-radius: 20rpx !important; /* iOS圆角 */
	border: 1rpx solid #c7c7cc !important; /* iOS浅边框 */
}

.elderly-mode .service-icon,
.ios-elderly-mode .service-icon {
	width: 80rpx !important; /* 增大图标 */
	height: 80rpx !important;
	border-radius: 20rpx !important;
	margin-bottom: 16rpx !important;
}

.elderly-mode .news-item,
.ios-elderly-mode .news-item {
	padding: 24rpx !important; /* 增大内边距 */
	min-height: 112rpx !important; /* 56pt触摸目标 */
	border-radius: 20rpx !important; /* iOS圆角 */
	border: 1rpx solid #c7c7cc !important; /* iOS边框 */
	margin-bottom: 16rpx !important; /* 增加间距 */
}

/* 适老化焦点状态 - iOS风格 */
.elderly-mode .function-item:focus,
.ios-elderly-mode .function-item:focus,
.elderly-mode .emergency-card:focus,
.ios-elderly-mode .emergency-card:focus,
.elderly-mode .service-item:focus,
.ios-elderly-mode .service-item:focus,
.elderly-mode .news-item:focus,
.ios-elderly-mode .news-item:focus {
	outline: none !important;
	box-shadow: 0 0 0 4rpx rgba(255, 138, 0, 0.4) !important; /* 焦点环 */
	border-color: #ff8a00 !important; /* 品牌色边框 */
}

/* 适老化按压反馈增强 - iOS风格 */
.elderly-mode .function-item:active,
.ios-elderly-mode .function-item:active {
	transform: scale(0.90) !important; /* 更明显的按压效果 */
	background: rgba(255, 138, 0, 0.1) !important; /* 按压背景 */
}

.elderly-mode .emergency-card:active,
.ios-elderly-mode .emergency-card:active {
	transform: scale(0.92) !important;
	box-shadow:
		0 2rpx 8rpx rgba(0, 0, 0, 0.15),
		0 4rpx 16rpx rgba(0, 0, 0, 0.12) !important;
}

.elderly-mode .service-item:active,
.ios-elderly-mode .service-item:active,
.elderly-mode .news-item:active,
.ios-elderly-mode .news-item:active {
	transform: scale(0.95) !important;
	background: rgba(255, 138, 0, 0.05) !important;
}
</style>
